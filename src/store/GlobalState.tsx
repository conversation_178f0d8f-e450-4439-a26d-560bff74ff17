"use client";

import { createContext, useReducer, ReactNode } from "react";
import reducers from "./Reducers";

// Create the context with a default value
export const DataContext = createContext<any>(undefined);

// Define the type for the provider props
interface DataProviderProps {
  children: ReactNode;
}

export const DataProvider = ({ children }: DataProviderProps) => {
  const initialState = {
    user: null,
    token: null,
    callback: false,
    loading: false,
    channelloading: true,
    notify: null,
    onlineStatus: "offline",
    channels: null,
    otherChannels: null,
    thread: null,
    webhookStatus: null,
    channelDetails: null,
    channelModal: false,
    members: null,
    archivedChannels: null,
    channelBar: false,
    visible: false,
    visibles: false,
    showThread: false,
    openSidebar: false,
    appCallback: false,
    show: null,
    createChannel: false,
    createSection: false,
    addMember: false,
    orgMembers: null,
    orgInvite: null,
    orgRoles: null,
    showRange: false,
    summaryCount: 0,
    sectionId: null,
    sections: null,
    deleteSection: false,
    ungroupedChannels: null,
    isOpened: "",
    notifications: [],
    messages: [],
    chats: [],
    dms: [],
    replies: [],
    integrations: [],
    messageLoading: true,
    integrationsLoading: true,
    chatLoading: true,
    replyLoading: true,
    subscriptionPlan: null,
    orgId: null,
    notificationType: null,
    notificationCallback: false,

    // ----------------------
    orgData: null,
    channelCallback: false,
    channelSubscription: null,
    chatSubscription: null,
    replySubscription: null,
    agentDm: [],
    channelAgents: [],
    mentions: [],
    inviteModal: false,
    showProfile: false,
    hoverProfile: false,
    reply: false,
    recentPeople: [],
    recentDm: [],
    profile: null,
    showUserProfile: false,
    profileCallback: false,
    groupCallback: false,
    replyCallback: false,
    userTyping: [],
    typingSubscription: null,
    notificationSubscription: null,
    dmNotificationSubscription: null,
    isEdit: false,
    isEditReply: false,
    threadReply: null,
    role: "",
    statusCallback: false,
    topLabel: "Active",
    activeAgents: [],
    inactiveAgents: [],
    marketPlaceAgents: [],
    marketPlaceAgentsCacheTime: null,
    countCallback: false,
    threadCount: 0,
    dmCount: 0,
    showBadge: false,
    userData: null,
    status: false,
    notificationDetail: null,
    channelInvite: false,
    agentModal: false,
    agentState: null,
    agentCallback: false,
    bookmarks: [],
    pinned: [],

    // subscriptions
    subscriptionPlans: null,
    currentSubscription: null,
    activeTab: "about",
    later: [],
    dataId: "",
    allChannels: [],
    workflows: [],
    workflowCallback: false,
    channelWorkflows: [],
    activeWorkflows: [],
    inactiveWorkflows: [],
    marketPlaceWorkflows: [],
    media: [],
    medias: [],
    selectedSkills: [],
  };

  const [state, dispatch] = useReducer(reducers, initialState);

  return (
    <DataContext.Provider value={{ state, dispatch }}>
      {children}
    </DataContext.Provider>
  );
};
