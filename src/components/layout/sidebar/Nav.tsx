"use client";
import { useContext, useEffect, useRef, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { useRouter } from "next/navigation";
import { ChevronRight, PlusIcon } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { ChatBubbleIcon, DropdownIcon, PencilIcon } from "~/svgs";
import { ChannelCard } from "~/app/(client)/client/_components/ChannelCard";
import { cn } from "~/lib/utils";
import { PeopleHomeCard } from "~/app/(client)/client/_components/people-card";
import Link from "next/link";
import InviteModal from "~/app/(client)/client/_components/invite-modal";
import ChannelInviteModal from "~/app/(client)/client/_components/invite-modal/channel";
import OrganisationMenu from "~/app/(client)/client/_components/org-dropdown";
import AddColleagueDialog from "~/app/(client)/client/_components/colleagues/add-colleagues-modal";
import { ColleaguesCard } from "~/app/(client)/client/_components/colleagues/card";

//

export default function ChannelNav() {
  const { state, dispatch } = useContext(DataContext);
  const router = useRouter();

  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Restore scroll position after re-render
  useEffect(() => {
    const savedScroll = sessionStorage.getItem("sidebar-scroll");
    if (sidebarRef.current && savedScroll) {
      sidebarRef.current.scrollTop = parseInt(savedScroll, 10);
    }
  }, []);

  // Save scroll position before re-render
  const handleScroll = () => {
    if (sidebarRef.current) {
      sessionStorage.setItem(
        "sidebar-scroll",
        sidebarRef.current.scrollTop.toString()
      );
    }
  };

  const [openAccordions, setOpenAccordions] = useState<string[]>(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("openAccordions");
      return saved
        ? JSON.parse(saved)
        : ["channels", "workflows", "agents", "people"];
    }
    return ["channels", "workflows", "agents", "people"];
  });

  // Function to toggle accordions independently
  const handleAccordionChanges = (values: string[]) => {
    setOpenAccordions(values);
    if (typeof window !== "undefined") {
      localStorage.setItem("openAccordions", JSON.stringify(values));
    }
  };

  const handleNewChat = () => {
    dispatch({ type: ACTIONS.CLEAR_CHATS });
    router.push("/client/home/<USER>/new-chat");
  };

  //

  return (
    <>
      <div
        className={`fixed top-[60px] bottom-[60px] left-0 lg:left-[145px] h-[calc(100vh-30px)] bg-blue-300 lg:translate-x-0 ${state?.channelBar === true && state?.openSidebar ? "translate-x-[145px]" : state?.channelBar === true && !state?.openSidebar ? "translate-x-0" : "-translate-x-full "}
      pt-4 flex flex-col gap-6 sm:w-[350px] transition-transform duration-300 ease-in-out z-20`}
      >
        <div className="flex items-center justify-between px-4 ">
          <div className="flex items-center gap-[5px] justify-between w-full">
            <OrganisationMenu />

            <button type="button" onClick={handleNewChat}>
              <PencilIcon />
            </button>
          </div>
        </div>

        <div
          className="overflow-auto [&::-webkit-scrollbar]:hidden text-blue-50 cursor-pointer pb-20"
          ref={sidebarRef}
          onScroll={handleScroll}
          onClick={() =>
            dispatch({ type: ACTIONS.CHANNEL_BAR, payload: false })
          }
          style={{ WebkitOverflowScrolling: "touch" }}
        >
          <div className="flex-1 flex items-center gap-[2px] py-1 px-2 mx-2 hover:bg-blue-200 hover:text-white rounded-lg ">
            <ChatBubbleIcon />
            <p
              className={cn(
                "text-[15px] leading-4 truncate w-[180px] text-blue-50"
              )}
            >
              Threads
            </p>
          </div>

          <div className="">
            <div>
              <Accordion
                type="multiple"
                className="w-full"
                value={openAccordions}
                onValueChange={handleAccordionChanges}
                onClick={(e) => e.stopPropagation()}
              >
                <AccordionItem value="channels" className="border-none">
                  <AccordionTrigger className="font-normal w-full py-0">
                    <div className="relative py-3 mx-4 flex items-center gap-1 rounded-lg cursor-pointer w-full">
                      <DropdownIcon
                        className={`transition-transform duration-300 ${
                          openAccordions.includes("channels")
                            ? "rotate-0"
                            : "-rotate-90"
                        }`}
                      />
                      <h3 className="text-[15px]  font-medium">Channels</h3>
                    </div>
                  </AccordionTrigger>

                  <AccordionContent>
                    <ul className="flex flex-col gap-1">
                      {state?.channels?.map((item: any, index: number) => (
                        <ChannelCard {...item} key={index} />
                      ))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <Link
                href="/client/home/<USER>"
                className={cn(
                  "relative px-2 mx-2 py-[6px] flex items-center justify-between rounded-lg group hover:border-blue-200 border border-[#4B4BB4] cursor-pointer"
                )}
              >
                <p
                  className={cn("text-[14px] leading-4 truncate text-blue-50")}
                >
                  View all channels
                </p>

                <ChevronRight size={17} />
              </Link>
            </div>

            {/* colleagues */}
            <>
              <Accordion
                type="multiple"
                className="w-full mt-4"
                value={openAccordions}
                onValueChange={handleAccordionChanges}
                onClick={(e) => e.stopPropagation()}
              >
                <AccordionItem value="workflows" className="border-none">
                  <AccordionTrigger className="font-normal w-full py-0">
                    <div className="relative py-3 mx-4 flex items-center gap-1 rounded-lg cursor-pointer w-full">
                      <DropdownIcon
                        className={`w-5 h-5 transition-transform duration-300 ${
                          openAccordions.includes("workflows")
                            ? "rotate-0"
                            : "-rotate-90"
                        }`}
                      />
                      <h3 className="text-[15px]  font-medium">Colleagues</h3>
                    </div>
                  </AccordionTrigger>

                  <AccordionContent>
                    <ul className="flex flex-col gap-1">
                      {state?.agentDm?.map((item: any, index: number) => (
                        <ColleaguesCard {...item} key={index} />
                      ))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <AddColleagueDialog />
            </>

            {/* Agents */}
            {/* <>
              <Accordion
                type="multiple"
                className="w-full mt-4"
                value={openAccordions}
                onValueChange={handleAccordionChanges}
                onClick={(e) => e.stopPropagation()}
              >
                <AccordionItem value="agents" className="border-none">
                  <AccordionTrigger className="font-normal w-full py-0">
                    <div className="relative py-3 mx-4 flex items-center gap-1 rounded-lg cursor-pointer w-full">
                      <DropdownIcon
                        className={`w-5 h-5 transition-transform duration-300 ${
                          openAccordions.includes("agents")
                            ? "rotate-0"
                            : "-rotate-90"
                        }`}
                      />
                      <h3 className="text-[15px]  font-medium">Agents</h3>
                    </div>
                  </AccordionTrigger>

                  <AccordionContent>
                    <ul className="flex flex-col gap-1">
                      {state?.agentDm?.map((item: any, index: number) => (
                        <AgentCard {...item} key={index} />
                      ))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <Link
                href="/client/agents/browse-agents"
                className={cn(
                  "relative px-2 mx-2 py-[6px] mb-3 flex items-center justify-between rounded-lg group hover:border-blue-200 border border-[#4B4BB4] cursor-pointer"
                )}
              >
                <p
                  className={cn("text-[14px] leading-4 truncate text-blue-50")}
                >
                  View all agents
                </p>

                <ChevronRight size={17} />
              </Link>
            </> */}
          </div>

          {!state?.channelloading && (
            <Accordion
              type="multiple"
              className="w-full"
              value={openAccordions}
              onValueChange={handleAccordionChanges}
              onClick={(e) => e.stopPropagation()}
            >
              <AccordionItem value="people" className="border-none">
                <div className="relative py-3 mx-4 flex items-center justify-between rounded-lg cursor-pointer z-50 group">
                  <AccordionTrigger className="font-normal py-0">
                    <div className="relative flex items-center gap-1 rounded-lg cursor-pointer">
                      <DropdownIcon
                        className={`w-5 h-5 transition-transform duration-300 ${
                          openAccordions.includes("people")
                            ? "rotate-0"
                            : "-rotate-90"
                        }`}
                      />
                      <h3 className="text-[15px] leading-4 font-medium capitalize">
                        People
                      </h3>
                    </div>
                  </AccordionTrigger>

                  <div
                    className="flex items-center justify-center h-6 w-6 bg-blue-500 rounded gap-1 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={handleNewChat}
                  >
                    <PlusIcon className="size-4" />
                  </div>
                </div>

                <AccordionContent>
                  {state?.dms?.map((item: any, index: number) => (
                    <div className="mb-1" key={index}>
                      <PeopleHomeCard {...item} />
                    </div>
                  ))}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}

          <Link
            href="/client/home/<USER>"
            className={cn(
              "relative px-2 mx-2 py-[6px] mb-10 flex items-center justify-between rounded-lg group hover:border-blue-200 border border-[#4B4BB4] cursor-pointer"
            )}
          >
            <p className={cn("text-[14px] leading-4 truncate text-blue-50")}>
              View all people
            </p>

            <ChevronRight size={17} />
          </Link>
        </div>
      </div>

      {state?.inviteModal && <InviteModal />}
      {state?.channelInvite && <ChannelInviteModal />}
    </>
  );
}
