import React from "react";

export default function AgentsContentLoading() {
  return (
    <div className="flex items-center justify-center">
      <div className="w-full py-16 px-2 md:px-6 pb-40  flex items-center justify-center">
        <div className=" w-full grid grid-cols-1 sm:grid-cols-2 max-w-7xl">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-white shadow-xs py-8 overflow-hidden space-y-4 border border-dashed border-spacing-96 border-[#E9E9E9]">
                <div className="flex items-center justify-between px-5 xl:px-8">
                  <div className="w-9 h-9 bg-gray-300 rounded-full"></div>
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                </div>
                <div className="px-5 xl:px-8 border-l-2 border-gray-300">
                  <div className="h-6 bg-gray-300 rounded mb-3"></div>
                </div>
                <div className="px-5 xl:px-8">
                  <div className="space-y-2 mb-4">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
                <div className="px-5 xl:px-8">
                  <div className="h-10 bg-gray-200 rounded-xl w-24"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
