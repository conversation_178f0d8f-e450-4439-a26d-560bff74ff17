"use client";

import React, { useContext, useEffect, useState } from "react";
import <PERSON><PERSON><PERSON> from "./agents-hero";
import AgentsTop from "./agents-top";
import HorizontalCarousel from "./agents-carousel";
import AgentsContent from "./agents-content";
import AgentsContentLoading from "./agents-content-loading";
import AgentsContact from "./agents-contact";
import Image from "next/image";
import { Agent } from "../page";
import { fetchAgents } from "../lib/api";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { availableCategories } from "../data/agents";

export interface CategoryAgentsPageClientProps {
  categoryName: string;
}

// Transform backend agent data to match the existing component interface
interface LegacyAgentItem {
  id: string;
  section: string;
  title: string;
  excerpt: string;
  bot: string;
  color: string;
}

const transformAgentData = (agents: Agent[]): LegacyAgentItem[] => {
  return agents.map((agent) => ({
    id: agent.id,
    section: agent.category || "General",
    title: agent.app_name,
    excerpt: agent.app_description,
    bot: agent.app_logo || "/images/bot-gray.svg", // fallback image
    color: getCategoryColor(agent.category),
  }));
};

const getCategoryColor = (category: string): string => {
  const colorMap: { [key: string]: string } = {
    // Legacy categories
    "Application Performance Monitoring": "#1D5A69",
    "Cloud Monitoring": "#344054",
    "Website Testing": "#700902",
    "Webhook Testing": "#2E8DFF",
    "Social Media": "#009143",
    Agent: "#7141F8",
    // New categories
    "Customer Support": "#10B981",
    "Sales and Marketing": "#F59E0B",
    "Engineering DevOps": "#3B82F6",
    "Documents and Analysis": "#8B5CF6",
    "Strategy Business": "#EF4444",
    "Content and Video": "#EC4899",
    "": "#344054", // default color
  };
  return colorMap[category] || "#344054";
};

export default function CategoryAgentsPageClient({
  categoryName,
}: CategoryAgentsPageClientProps) {
  const { state, dispatch } = useContext(DataContext);
  const [agentsSearch, setAgentsSearch] = useState("");
  const [agentsCategory, setAgentsCategory] = useState(categoryName);
  const [agentsDisplayed, setAgentsDisplayed] = useState<LegacyAgentItem[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [allAgents, setAllAgents] = useState<LegacyAgentItem[]>([]);
  const [isLoadingAgents, setIsLoadingAgents] = useState(true);

  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    monitoringNeed: "",
  });

  const isFormValid =
    form.firstName && form.lastName && form.email && form.monitoringNeed;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  const handleAgentsSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setAgentsSearch("");
  };

  const handleAgentsChange = () => {
    let filteredAgents = [...allAgents];

    // Filter by category - if "All" is selected, show all agents from this category
    // If the specific category is selected, show only that category
    if (agentsCategory === "All") {
      filteredAgents = filteredAgents.filter(
        (item) => item.section === categoryName
      );
    } else {
      filteredAgents = filteredAgents.filter(
        (item) => item.section === agentsCategory
      );
    }

    // Filter by search term
    if (agentsSearch !== "") {
      filteredAgents = filteredAgents.filter((item) =>
        item.title.toLowerCase().includes(agentsSearch.toLowerCase())
      );
    }

    setAgentsDisplayed(filteredAgents);
  };

  // Function to refresh agents cache
  const refreshAgentsCache = async () => {
    try {
      setIsLoadingAgents(true);
      console.log("Refreshing agents cache");
      const agentsData = await fetchAgents();
      const now = Date.now();

      // Update global state cache with new data and timestamp
      dispatch({ type: ACTIONS.MARKETPLACE_AGENTS, payload: agentsData });
      dispatch({ type: ACTIONS.MARKETPLACE_AGENTS_CACHE_TIME, payload: now });

      // Transform and set local state
      const transformedAgents = transformAgentData(agentsData);
      setAllAgents(transformedAgents);
    } catch (error) {
      console.error("Error refreshing agents cache:", error);
    } finally {
      setIsLoadingAgents(false);
    }
  };

  // Fetch agents data on component mount with caching
  useEffect(() => {
    const loadAgents = async () => {
      try {
        setIsLoadingAgents(true);

        // Cache expiration time (5 minutes)
        const CACHE_EXPIRY_MS = 5 * 60 * 1000;
        const now = Date.now();
        const cacheTime = state.marketPlaceAgentsCacheTime;
        const isCacheValid = cacheTime && now - cacheTime < CACHE_EXPIRY_MS;

        // Check if agents are already cached and cache is still valid
        if (
          state.marketPlaceAgents &&
          state.marketPlaceAgents.length > 0 &&
          isCacheValid
        ) {
          console.log("Loading agents from cache", {
            agentsCount: state.marketPlaceAgents.length,
            cacheAge: Math.round((now - cacheTime) / 1000) + "s",
          });
          const transformedAgents = transformAgentData(state.marketPlaceAgents);
          setAllAgents(transformedAgents);
          setIsLoadingAgents(false);
          return;
        }

        // Fetch from API if not cached or cache expired
        const reason =
          !state.marketPlaceAgents || state.marketPlaceAgents.length === 0
            ? "no cache"
            : "cache expired";
        console.log(`Fetching agents from API (${reason})`);
        const agentsData = await fetchAgents();

        // Cache the raw agents data and timestamp in global state
        dispatch({ type: ACTIONS.MARKETPLACE_AGENTS, payload: agentsData });
        dispatch({ type: ACTIONS.MARKETPLACE_AGENTS_CACHE_TIME, payload: now });

        // Transform and set local state
        const transformedAgents = transformAgentData(agentsData);
        setAllAgents(transformedAgents);
      } catch (error) {
        console.error("Error loading agents:", error);
        setAllAgents([]);
      } finally {
        setIsLoadingAgents(false);
      }
    };

    loadAgents();
  }, [state.marketPlaceAgents, state.marketPlaceAgentsCacheTime, dispatch]);

  // Handle filtering when search or category changes
  useEffect(() => {
    handleAgentsChange();
  }, [agentsCategory, agentsSearch, allAgents]);

  // Expose cache utilities to window for debugging (development only)
  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      process.env.NODE_ENV === "development"
    ) {
      (window as any).agentsCache = {
        clear: () => {
          dispatch({ type: ACTIONS.MARKETPLACE_AGENTS, payload: [] });
          dispatch({
            type: ACTIONS.MARKETPLACE_AGENTS_CACHE_TIME,
            payload: null,
          });
          console.log("Agents cache cleared");
        },
        refresh: refreshAgentsCache,
        status: () => {
          const cacheTime = state.marketPlaceAgentsCacheTime;
          const now = Date.now();
          const age = cacheTime ? Math.round((now - cacheTime) / 1000) : null;
          console.log("Cache status:", {
            hasData: !!(
              state.marketPlaceAgents && state.marketPlaceAgents.length > 0
            ),
            count: state.marketPlaceAgents?.length || 0,
            ageSeconds: age,
            isValid: age ? age < 300 : false, // 5 minutes
          });
        },
      };
    }
  }, [
    state.marketPlaceAgents,
    state.marketPlaceAgentsCacheTime,
    dispatch,
    refreshAgentsCache,
  ]);

  return (
    <>
      <AgentsHero
        agentsSearch={agentsSearch}
        handleAgentsSearch={handleAgentsSearch}
        setAgentsSearch={setAgentsSearch}
      />
      <div className="space-y-5">
        <AgentsTop
          agentsSearch={agentsSearch}
          handleAgentsSearch={handleAgentsSearch}
          setAgentsSearch={setAgentsSearch}
          agentsCount={agentsDisplayed.length}
        />
        <HorizontalCarousel
          setAgentsCategory={setAgentsCategory}
          categories={availableCategories}
        />
      </div>
      {isLoadingAgents ? (
        <AgentsContentLoading />
      ) : (
        <AgentsContent
          agentsDisplayed={agentsDisplayed}
          setOpenDialog={setOpenDialog}
        />
      )}
      <AgentsContact />
      {openDialog && (
        <div className="fixed top-0 right-0 left-0 bottom-0 flex items-center backdrop-blur-sm justify-center max-h-screen z-50">
          <div className="w-[90%] md:w-[60%] md:h-[70%]">
            <div className="flex border border-blue-800 rounded-lg w-full h-full bg-white ">
              <div className="rounded-lg hidden md:flex w-2/5">
                <Image
                  src={"/images/agents-request-image.svg"}
                  height={607}
                  width={422}
                  className="rounded-lg object-cover w-full h-full"
                  alt="Request Agent Image"
                />
              </div>
              <div className="rounded-lg w-full md:w-3/5">
                <form action="" className="p-5 space-y-6">
                  <div className="">
                    <h2 className="font-bold text-xl">Request an Agent</h2>
                    <p className="text-sm text-[#475467]">
                      {`Can't find an agent you need? Feel free to shoot us a
                      request and we will try to bring them on.`}
                    </p>
                  </div>
                  <div className="flex gap-4">
                    <div className="flex flex-col text-sm w-1/2 gap-2">
                      <label htmlFor="firstName">First Name</label>
                      <input
                        type="text"
                        placeholder="John"
                        className="text-[#475467] border outline-none rounded-md py-3 px-4 w-full"
                        onChange={handleChange}
                        value={form.firstName}
                        name="firstName"
                      />
                    </div>
                    <div className="flex flex-col text-sm w-1/2 gap-2">
                      <label htmlFor="lastName">Last Name</label>
                      <input
                        type="text"
                        placeholder="Doe"
                        className="text-[#475467] border outline-none rounded-md py-3 px-4 w-full"
                        onChange={handleChange}
                        value={form.lastName}
                        name="lastName"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col text-sm gap-2">
                    <label htmlFor="email">Email</label>
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      className="text-[#475467] border outline-none rounded-md py-3 px-4"
                      onChange={handleChange}
                      value={form.email}
                      name="email"
                    />
                  </div>
                  <div className="flex flex-col text-sm gap-2">
                    <label htmlFor="monitoringNeed">Monitoring Need</label>
                    <input
                      type="text"
                      placeholder="E.g. I need an agent to monitor API response times"
                      maxLength={80}
                      max={80}
                      className=" border outline-none rounded-md py-3 px-4"
                      onChange={handleChange}
                      value={form.monitoringNeed}
                      name="monitoringNeed"
                    />
                  </div>
                  <div className="flex items-center gap-4 justify-end text-sm">
                    <button
                      onClick={() => setOpenDialog(false)}
                      className="border px-4 py-3 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      disabled={!isFormValid}
                      className={`px-4 py-3 border rounded-md ${!isFormValid ? "opacity-60 cursor-not-allowed" : "opacity-100"}`}
                    >
                      Send Request
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
