import React from "react";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import { getAllCategorySlugs, getCategoryConfig } from "../../lib/categories";
import CategoryAgentsPageClient from "../../components/category-agents-page-client";

interface CategoryPageProps {
  params: {
    category: string;
  };
}

// Generate static params for all categories
export async function generateStaticParams() {
  const categorySlugs = getAllCategorySlugs();

  return categorySlugs.map((category) => ({
    category,
  }));
}

// Generate metadata for each category
export async function generateMetadata({
  params,
}: CategoryPageProps): Promise<Metadata> {
  const categoryConfig = getCategoryConfig(params.category);

  if (!categoryConfig) {
    return {
      title: "Category Not Found | Telex",
      description: "The requested agent category could not be found.",
    };
  }

  return categoryConfig.metadata;
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const categoryConfig = getCategoryConfig(params.category);

  // If category doesn't exist, show 404
  if (!categoryConfig) {
    notFound();
  }

  return (
    <div className="bg-white relative">
      <CategoryAgentsPageClient categoryName={categoryConfig.name} />
    </div>
  );
}
