"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronLeft, Star } from "lucide-react";
import { useRouter } from "next/navigation";
import { PostRequest } from "~/utils/new-request";
import { Agent } from "../../../page";

interface AgentHeaderProps {
  agent: Agent;
}

const AgentHeader = ({ agent }: AgentHeaderProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(false);

  const handleUseAgent = async () => {
    setIsLoading(true);

    try {
      // Check if user is authenticated
      const token = localStorage.getItem("token");

      if (!token) {
        // User is not authenticated, redirect to login with return URL parameter
        const intendedRoute = `/client/agents/browse-agents/${agent.id}?json_url=${encodeURIComponent(agent.json_url)}`;
        const loginUrl = `/auth/login?redirect=${encodeURIComponent(intendedRoute)}`;
        router.push(loginUrl);
        return;
      }

      // User is authenticated, navigate to the agent in the dashboard
      const orgId = localStorage.getItem("orgId") || "";

      // Create a DM channel with the agent
      const payload = {
        chat_type: "bot",
        participant_id: agent.id,
      };

      // Store agent name for the channel
      localStorage.setItem("channelName", agent.app_name);

      const res = await PostRequest(`/organisations/${orgId}/dms`, payload);

      if (res?.status === 200 || res?.status === 201) {
        // Navigate to the agent chat page
        router.push(
          `/client/home/<USER>/${res?.data?.data?.channel_id}/${res?.data?.data?.participant_id}`
        );
      } else {
        // If DM creation fails, fallback to browse agents page
        router.push(
          `/client/agents/browse-agents/${agent.id}?json_url=${encodeURIComponent(agent.json_url)}`
        );
      }
    } catch (error) {
      console.error("Error navigating to agent:", error);
      // Fallback to browse agents page on error
      router.push(
        `/client/agents/browse-agents/${agent.id}?json_url=${encodeURIComponent(agent.json_url)}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mt-10">
      <div className="flex items-center gap-2 text-sm text-gray-600 mb-6">
        <Link
          href="/agents"
          className="flex items-center gap-1 hover:text-gray-900 transition-colors"
        >
          <ChevronLeft size={16} />
          Go to Store
        </Link>
      </div>

      {/* Agent Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-4">
          {/* Agent Logo */}
          <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
            {agent.app_logo ? (
              <Image
                src={agent.app_logo}
                alt={agent.app_name}
                width={64}
                height={64}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                  {agent.app_name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>

          {/* Agent Info */}
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {agent.app_name}
            </h1>

            {/* Provider Info */}
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
              <span>
                Developed by{" "}
                <span className="font-medium text-gray-900">
                  {agent.provider?.organization || "Telex"}
                </span>
              </span>
            </div>

            {/* Description */}
            <p className="text-gray-700 mb-4 max-w-2xl">
              {agent.app_description}
            </p>

            {/* Stats */}
            <div className="flex items-center gap-6 text-sm">
              <div className="flex items-center gap-1">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      size={16}
                      className="text-yellow-400 fill-current"
                    />
                  ))}
                </div>
                <span className="font-medium">5.0 (93)</span>
              </div>

              <div className="flex items-center gap-1">
                <span className="text-gray-600">Pricing:</span>
                <span className="font-medium">Pay per event</span>
              </div>
              <div className="text-xs text-gray-500">
                Created at{" "}
                {new Date(agent.created_at).toLocaleString("en-US", {
                  dateStyle: "medium",
                  timeStyle: "short",
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-3">
          <button
            onClick={handleUseAgent}
            disabled={isLoading}
            className="bg-primary-500 text-xs md:text-sm text-white font-semibold py-3 px-6 rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition duration-300 ease-in-out mb-3"
          >
            <span className="text-sm text-white cursor-pointer hover:underline">
              {isLoading ? "Loading..." : "Use this Agent in Telex"}
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AgentHeader;
