import { MoreHorizontal } from "lucide-react";
import Image from "next/image";
// import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { SmallSwitch } from "~/components/ui/small-switch";

interface Skills {
  name: string;
  icon: string;
  id: string;
  description: string;
  is_active: boolean;
}

const SkillCard = (props: Skills) => {
  //   const [isActive, setIsActive] = useState(false);

  //   const handleSwitchChange = async (checked: boolean, id: string) => {
  //     // const orgId = localStorage.getItem("orgId") || "";
  //     // const payload = {
  //     //   integration_id: id,
  //     //   status: checked,
  //     // };
  //     // const res = await PatchRequest(
  //     //   `/organisations/${orgId}/agents/change_status`,
  //     //   payload
  //     // );
  //     // if (res?.status === 200 || res?.status === 201) {
  //     //   setIsActive(checked);
  //     //   setAgent((prev: any) => ({
  //     //     ...prev,
  //     //     is_active: checked,
  //     //   }));
  //     //   dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });
  //     // }
  //   };

  //   remove
  const handleRemove = () => {};

  return (
    <div
      className={`w-full border rounded-lg transition-all border-2 border-gray-200 hover:border-primary-300`}
    >
      <div className="flex items-start justify-between gap-3 pt-4 pb-2 px-4">
        <div className="flex items-center gap-3 mb-2">
          <div
            className={`w-8 h-8 rounded-md flex items-center justify-center text-white text-sm bg-[#F1F1FE] border`}
          >
            <Image
              src={props?.icon}
              alt="skill"
              width={20}
              height={20}
              unoptimized
            />
          </div>
          <h4 className="font-medium text-sm text-gray-900">{props?.name}</h4>
        </div>

        <div className="flex items-center gap-2 text-gray-500">
          <div
            className={`flex items-center gap-1 rounded-3xl border ${props?.is_active ? "border-[#7141F8]" : ""} py-4 px-3 h-0`}
          >
            <span
              className={`text-[10px] ${props?.is_active ? "text-[#7141F8]" : "text-[#667085]"}`}
            >
              {props?.is_active ? "Active" : "Disabled"}
            </span>

            <SmallSwitch
              className="bg-green-500"
              //   checked={isActive}
              //   onCheckedChange={(checked) => {
              //     handleSwitchChange(checked, props?.id);
              //   }}
              size="sm"
            />
          </div>
          |
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="p-1 rounded hover:bg-gray-100 border">
                <MoreHorizontal className="w-4 h-4 text-gray-600" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-44">
              <DropdownMenuItem
                className="cursor-pointer hover:bg-gray-100"
                // onClick={onEdit}
              >
                Edit Configuration
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer text-red-500 focus:text-red-500 hover:bg-gray-100"
                onClick={handleRemove}
              >
                Remove Skill
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <hr />

      <div className="flex-1 min-w-0 p-4">
        <p className="text-xs text-[#667085] leading-relaxed">
          {props?.description}
        </p>
      </div>
    </div>
  );
};

export default SkillCard;
