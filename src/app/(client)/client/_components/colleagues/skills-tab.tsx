import Image from "next/image";
import { useContext, useState } from "react";
import { SkillsDialog } from "./skills-dialog";
import { DataContext } from "~/store/GlobalState";
import SkillCard from "./skill-card";

const Skills = () => {
  const [openChange, setOpenChange] = useState(false);
  const { state } = useContext(DataContext);
  const { selectedSkills } = state;

  return (
    <div className="my-5">
      {selectedSkills?.length === 0 && (
        <div className="flex flex-col items-center justify-center space-y-1 mt-20">
          <Image
            src="/image/empty-box.svg"
            alt="empty skills"
            width={250}
            height={250}
            unoptimized
          />
          <p className="">Juno doesn't have any skill yet</p>
          <SkillsDialog open={openChange} onOpenChange={setOpenChange} />
        </div>
      )}
      <div className="grid gap-5 grid-cols-1 sm:grid-cols-2">
        {selectedSkills?.map((item: any) => (
          <SkillCard {...item} key={item.id} />
        ))}
      </div>
    </div>
  );
};

export default Skills;
