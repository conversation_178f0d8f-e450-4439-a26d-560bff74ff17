"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogTrigger,
  DialogClose,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { PlusCircle, X } from "lucide-react";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "~/components/ui/select";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { cn } from "~/lib/utils";
import AvatarPicker from "./avatars";

export default function AddColleagueDialog() {
  const [open, setOpen] = React.useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div
          className={cn(
            "relative px-2 mx-2 mb-3 flex items-center gap-2 rounded-lg group cursor-pointer"
          )}
        >
          <PlusCircle size={18} className="" color="white" />
          <p className={cn("text-[14px] leading-4 truncate text-blue-50")}>
            Add New
          </p>
        </div>
      </DialogTrigger>

      <DialogContent className="max-w-lg rounded-xl px-0 py-6">
        <DialogHeader className="!flex !flex-row items-center justify-between px-6">
          <DialogTitle className="text-xl font-semibold">
            Create a New AI Colleague
          </DialogTitle>
          <DialogClose asChild>
            <button className="p-1 rounded hover:bg-gray-100">
              <X className="w-5 h-5" />
            </button>
          </DialogClose>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[480px] px-6 pt-4 border-t">
          {/* Image + Tone */}
          <div className="flex items-center gap-4 mt-4">
            <AvatarPicker
              isDialogOpen={isDialogOpen}
              setIsDialogOpen={setIsDialogOpen}
            />

            <div className="flex flex-col justify-start">
              <Label className="text-sm text-gray-700">Tone</Label>
              <Select defaultValue="friendly">
                <SelectTrigger className="w-40 mt-1 focus-ring-1 focus:ring-primary-500 focus-visible:ring-0 focus-visible:ring-primary">
                  <SelectValue placeholder="Select tone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="friendly">Friendly</SelectItem>
                  <SelectItem value="formal">Formal</SelectItem>
                  <SelectItem value="casual">Casual</SelectItem>
                </SelectContent>
              </Select>
              <button
                onClick={() => setIsDialogOpen(true)}
                className="text-xs text-start text-purple-500 mt-2"
              >
                ↻ Regenerate Face
              </button>
            </div>
          </div>

          {/* Fields */}
          <div className="space-y-4 mt-4">
            <div>
              <Label className="text-sm text-gray-700">Colleague name</Label>
              <Input placeholder="e.g Juno" className="mt-1" maxLength={40} />
            </div>

            <div>
              <Label className="text-sm text-gray-700">Colleague Title</Label>
              <Input
                placeholder="e.g Telegram Summariser"
                className="mt-1"
                maxLength={80}
              />
            </div>

            <div>
              <Label className="text-sm text-gray-700">Job description</Label>
              <Textarea
                placeholder={`e.g Monitor the #sales Telegram group and email me when "discount" is mentioned.`}
                className="mt-1 resize-none"
                maxLength={80}
              />
              <div className="text-xs text-gray-500 mt-1">18/80</div>
            </div>
          </div>

          {/* Colleague Visibility */}
          <div className="mt-6">
            <Label className="block mb-2 text-sm font-medium text-gray-700">
              Colleague Visibility
            </Label>
            <RadioGroup defaultValue="public" className="space-y-2">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="public" id="public" />
                <Label htmlFor="public" className="flex flex-col">
                  <span className="text-sm font-medium">
                    Public - anyone in Timbu Tech Ltd
                  </span>
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <RadioGroupItem value="private" id="private" />
                <Label htmlFor="private" className="flex flex-col">
                  <span className="text-sm font-medium">
                    Private - only specific people{" "}
                    <span className="text-gray-500 text-xs">
                      (can only be viewed or used by Admins)
                    </span>
                  </span>
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <RadioGroupItem value="me" id="me" />
                <Label htmlFor="me" className="flex flex-col">
                  <span className="text-sm font-medium">Me only</span>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Note */}
          <p className="text-xs text-gray-500 mt-4">
            <strong>Note:</strong> AI colleagues are personalized, chat-based
            agents that perform Tasks just like human teammates within Telex.
          </p>
        </div>

        <DialogFooter className="pt-6 flex justify-end gap-3 px-6 border-t">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            disabled
            className="bg-purple-500 hover:bg-purple-600 text-white"
          >
            Create Colleague
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
