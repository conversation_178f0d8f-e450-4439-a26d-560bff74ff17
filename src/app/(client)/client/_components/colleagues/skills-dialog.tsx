"use client";

import React, { useContext } from "react";
import {
  Search,
  Check,
  X,
  ArrowUpDown,
  CheckCircle,
  PlusIcon,
} from "lucide-react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Badge } from "~/components/ui/badge";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { AddSkillsIcon } from "~/svgs";
import Image from "next/image";
import { ACTIONS } from "~/store/Actions";
import { DataContext } from "~/store/GlobalState";

interface Skill {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
}

interface SelectedSkill {
  id: string;
  name: string;
  category: string;
  icon: string;
  description: string;
}

const skillsData: Skill[] = [
  {
    id: "telegram-monitor",
    name: "Telegram Monitor",
    description: "The bot will monitor your Telegram for specific activities.",
    category: "CRM",
    icon: "/image/telegram.png",
    color: "bg-blue-500",
  },
  {
    id: "gmail-access-1",
    name: "Gmail Access",
    description:
      "Let the AI colleague send, read, or monitor emails on behalf of the user via Gmail.",
    category: "Productivity",
    icon: "/image/gmail.png",
    color: "bg-red-500",
  },
  {
    id: "translation",
    name: "Translation",
    description:
      "Let the AI colleague translate texts from any source to any target language.",
    category: "Productivity",
    icon: "/image/telegram.png",
    color: "bg-purple-500",
  },
  {
    id: "gmail-access-2",
    name: "Gmail Access",
    description:
      "Let the AI colleague send, read, or monitor emails on behalf of the user via Gmail.",
    category: "Marketing",
    icon: "/image/gmail.png",
    color: "bg-red-500",
  },
  {
    id: "slack-integration",
    name: "Slack Integration",
    description: "Connect and manage Slack communications and workflows.",
    category: "CRM",
    icon: "/image/telegram.png",
    color: "bg-green-500",
  },
  {
    id: "data-analysis",
    name: "Data Analysis",
    description: "Analyze and process data from various sources.",
    category: "Devops",
    icon: "/image/telegram.png",
    color: "bg-blue-600",
  },
];

const categories = [
  { name: "All", count: skillsData.length },
  { name: "CRM", count: skillsData.filter((s) => s.category === "CRM").length },
  {
    name: "Devops",
    count: skillsData.filter((s) => s.category === "Devops").length,
  },
  {
    name: "Marketing",
    count: skillsData.filter((s) => s.category === "Marketing").length,
  },
  {
    name: "Productivity",
    count: skillsData.filter((s) => s.category === "Productivity").length,
  },
  {
    name: "Sales",
    count: skillsData.filter((s) => s.category === "Sales").length,
  },
];

export function SkillsDialog({
  open,
  onOpenChange,
}: {
  open: boolean;
  onOpenChange: any;
}) {
  const [selectedCategory, setSelectedCategory] = React.useState("All");
  const [selectedSkills, setSelectedSkills] = React.useState<SelectedSkill[]>(
    []
  );
  const [searchQuery, setSearchQuery] = React.useState("");
  const [activeTab, setActiveTab] = React.useState("prompt-based");
  const { dispatch } = useContext(DataContext);
  console.log;

  const filteredSkills = React.useMemo(() => {
    let filtered = skillsData;

    if (selectedCategory !== "All") {
      filtered = filtered.filter(
        (skill) => skill.category === selectedCategory
      );
    }

    if (searchQuery) {
      filtered = filtered.filter(
        (skill) =>
          skill.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          skill.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [selectedCategory, searchQuery]);

  const handleSkillSelect = (skill: Skill) => {
    const isSelected = selectedSkills.some((s) => s.id === skill.id);

    if (isSelected) {
      setSelectedSkills((prev) => prev.filter((s) => s.id !== skill.id));
    } else {
      setSelectedSkills((prev) => [
        ...prev,
        {
          id: skill.id,
          name: skill.name,
          category: skill.category,
          icon: skill.icon,
          description: skill.description,
        },
      ]);
    }
  };

  const handleSaveSkills = () => {
    dispatch({ type: ACTIONS.SELECTED_SKILLS, payload: selectedSkills });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger>
        <Button className="flex items-center gap-2 bg-primary-500 text-white px-10 mt-2">
          Add a skill
          <AddSkillsIcon />
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-5xl p-0 gap-0 overflow-hidden">
        <DialogHeader className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-semibold">
              Add a Skill to 'Juno'
            </DialogTitle>

            <DialogClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="p-1 border rounded h-6 w-6"
              >
                <X className="h-5 w-5" />
              </Button>
            </DialogClose>
          </div>
        </DialogHeader>

        {/* Tabs */}
        <div className="px-6 py-3 border-b">
          <div className="flex gap-6">
            <button
              className={`text-sm pb-2 border-b-2 transition-colors ${
                activeTab === "prompt-based"
                  ? "border-primary-500 text-blue-600 font-medium"
                  : "border-transparent text-[#667085] hover:text-gray-900"
              }`}
              onClick={() => setActiveTab("prompt-based")}
            >
              Prompt-Based Suggestions{" "}
              <span className="ml-1 text-xs bg-gray-200 px-1.5 py-0.5 rounded">
                3
              </span>
            </button>
            <button
              className={`text-sm pb-2 border-b-2 transition-colors ${
                activeTab === "company"
                  ? "border-primary-500 text-blue-600 font-medium"
                  : "border-transparent text-[#667085] hover:text-gray-900"
              }`}
              onClick={() => setActiveTab("company")}
            >
              Used by this company{" "}
              <span className="ml-1 text-xs bg-gray-200 px-1.5 py-0.5 rounded">
                48
              </span>
            </button>
            <button
              className={`text-sm pb-2 border-b-2 transition-colors ${
                activeTab === "others"
                  ? "border-primary-500 text-blue-600 font-medium"
                  : "border-transparent text-[#667085] hover:text-gray-900"
              }`}
              onClick={() => setActiveTab("others")}
            >
              Others{" "}
              <span className="ml-1 text-xs bg-gray-200 px-1.5 py-0.5 rounded">
                48
              </span>
            </button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden h-[70vh]">
          {/* Left Sidebar */}
          <div className="w-64 border-r p-4 overflow-y-auto">
            <h3 className="font-medium text-sm text-gray-900 mb-3">
              Filter by Category
            </h3>
            <Accordion type="multiple" className="w-full space-y-1">
              {categories.map((category) => (
                <AccordionItem
                  key={category.name}
                  value={category.name}
                  className="border-none"
                >
                  <AccordionTrigger
                    className={`hover:no-underline py-2 px-3 rounded-md text-sm font-normal justify-between ${
                      selectedCategory === category.name
                        ? "bg-[#F2F4F7] text-blue-600 shadow-sm"
                        : "hover:bg-[#F2F4F7]"
                    }`}
                    onClick={() => setSelectedCategory(category.name)}
                  >
                    <div className="flex items-center justify-between w-full">
                      <span>{category.name}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500">
                          ({category.count})
                        </span>
                        {selectedCategory === category.name && (
                          <Check className="h-3 w-3 text-blue-600" />
                        )}
                      </div>
                    </div>
                  </AccordionTrigger>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Search and Sort */}
            <div className="p-4 border-b bg-white">
              <div className="flex items-center gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Find a Skill"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-2 bg-transparent"
                    >
                      Sort By <ArrowUpDown className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>Name A-Z</DropdownMenuItem>
                    <DropdownMenuItem>Name Z-A</DropdownMenuItem>
                    <DropdownMenuItem>Category</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Skills Grid */}
            <div className="flex-1 overflow-y-auto p-4">
              <div className="grid grid-cols-2 gap-4">
                {filteredSkills.map((skill) => {
                  const isSelected = selectedSkills.some(
                    (s) => s.id === skill.id
                  );
                  return (
                    <div
                      key={skill.id}
                      className={`border rounded-lg transition-all ${
                        isSelected
                          ? "border-2 border-primary-500 shadow-sm"
                          : "border-2 border-gray-200 hover:border-primary-300"
                      }`}
                    >
                      <div className="flex items-start justify-between gap-3 pt-4 pb-2 px-4">
                        <div className="flex items-center gap-3 mb-2">
                          <div
                            className={`w-8 h-8 rounded-md flex items-center justify-center text-white text-sm bg-[#F1F1FE] border`}
                          >
                            <Image
                              src={skill.icon}
                              alt="skill"
                              width={20}
                              height={20}
                              unoptimized
                            />
                          </div>
                          <h4 className="font-medium text-sm text-gray-900">
                            {skill.name}
                          </h4>
                        </div>

                        <Button
                          variant={isSelected ? "default" : "ghost"}
                          size="sm"
                          className={`flex items-center gap-1 text-xs px-2 ${
                            isSelected
                              ? "text-gray-500"
                              : "text-blue-600 hover:text-blue-700"
                          }`}
                          onClick={() => handleSkillSelect(skill)}
                        >
                          {isSelected ? (
                            <CheckCircle size={13} />
                          ) : (
                            <PlusIcon size={15} className="mb-[2px]" />
                          )}
                          {isSelected ? "Selected" : "Select"}
                        </Button>
                      </div>

                      <hr />

                      <div className="flex-1 min-w-0 p-4">
                        <p className="text-xs text-[#667085] leading-relaxed">
                          {skill.description}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>

              {filteredSkills?.length === 0 && (
                <div className="flex flex-col mt-20 items-center justify-center">
                  <Image
                    src="/image/empty-box.svg"
                    alt="empty skills"
                    width={100}
                    height={100}
                    unoptimized
                  />
                  <p className="text-gray-400">Search not available</p>
                </div>
              )}
            </div>

            {/* Selected Skills */}
            {selectedSkills.length > 0 && (
              <div className="border-t bg-[#F1F1FE] p-2">
                <h4 className="text-xs font-medium text-gray-900 mb-3">
                  {selectedSkills.length} Selected Skill(s)
                </h4>
                <div className="flex flex-wrap gap-2">
                  {selectedSkills.map((skill) => (
                    <Badge
                      key={skill.id}
                      variant="secondary"
                      className="border gap-1 p-2 rounded-md text-blue-800 bg-white"
                    >
                      <Image
                        src={skill.icon}
                        alt="skill"
                        width={15}
                        height={15}
                        unoptimized
                      />
                      {skill.name}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1 hover:bg-gray-200"
                        onClick={() =>
                          setSelectedSkills((prev) =>
                            prev.filter((s) => s.id !== skill.id)
                          )
                        }
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Footer */}
            <div className="border-t bg-white p-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-[#667085]">
                  Can't find what you're looking for?{" "}
                  <button className="text-blue-600 hover:text-blue-700 underline">
                    Open Marketplace →
                  </button>
                </p>
                <div className="flex gap-3">
                  <Button variant="outline" onClick={() => onOpenChange(false)}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSaveSkills}
                    className="bg-primary-500 text-white"
                    disabled={selectedSkills.length === 0}
                  >
                    Add Skills
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
