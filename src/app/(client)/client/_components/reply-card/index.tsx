"use client";

import Image from "next/image";
import { format as timeformat } from "timeago.js";
import images from "~/assets/images";

type User = {
  id: number;
  avatar_url: string;
  user_type: string;
};

type ReplySummaryProps = {
  users: User[];
  totalReplies: number;
  lastReplyTime: string;
  handleReply: any;
};

export default function ReplyCard({
  users,
  totalReplies,
  lastReplyTime,
  handleReply,
}: ReplySummaryProps) {
  const visibleUsers = users?.slice(0, 4);
  const remainingUsers = users?.length - visibleUsers?.length;

  //

  return (
    <div className="flex items-center space-x-1 text-sm text-gray-600 mt-2">
      {/* Avatars */}
      <div className="flex space-x-[2px]">
        {visibleUsers?.map((user, index) => (
          <div
            key={index}
            className="w-6 h-6 size-5 rounded-md border overflow-hidden"
          >
            <Image
              src={
                user?.avatar_url
                  ? user?.avatar_url
                  : user?.user_type == "user" || user?.user_type === ""
                    ? images?.user
                    : images?.bot
              }
              alt={`User ${user.id}`}
              width={24}
              height={24}
              className="size-5 object-cover"
            />
          </div>
        ))}

        {remainingUsers > 0 && (
          <div className="w-6 h-6 rounded-full bg-gray-300 border-2 border-white text-xs flex items-center justify-center text-gray-700 font-medium">
            +{remainingUsers}
          </div>
        )}
      </div>

      {/* Replies count and time */}
      {visibleUsers?.length > 0 && (
        <>
          <span
            onClick={handleReply}
            className="text-blue-600 hover:underline cursor-pointer ml-2"
          >
            {totalReplies} replies
          </span>
          <span className="text-gray-500 ml-1">
            Last reply {timeformat(lastReplyTime)}
          </span>
        </>
      )}
    </div>
  );
}
