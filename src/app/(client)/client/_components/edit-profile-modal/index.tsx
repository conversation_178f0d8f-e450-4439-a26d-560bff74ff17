"use client";
import React, { useState, useRef, useContext, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Upload } from "lucide-react";
import Image from "next/image";
import { DataContext } from "~/store/GlobalState";
import images from "~/assets/images";
import cogoToast from "cogo-toast";
import Loading from "~/components/ui/loading";
import axios from "axios";
import { ACTIONS } from "~/store/Actions";
import { timezones } from "./timezones";
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

const EditProfileDialog = ({ isOpen, onClose }: any) => {
  const { state, dispatch } = useContext(DataContext);
  const { user } = state;
  const [fullName, setFullName] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [title, setTitle] = useState("");
  const [avatar, setAvatar] = useState<any>("");
  const [namePronunciation, setNamePronunciation] = useState("");
  const [timezone, setTimezone] = useState(user?.timezone);
  const [image, setImage] = useState("");
  const [phone, setPhone] = useState("");
  const [buttonLoading, setButtonLoading] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // set default values
  useEffect(() => {
    setFullName(user?.full_name || "");
    setDisplayName(user?.display_name || "");
    setAvatar(user?.avatar_url || images?.user);
    setTitle(user?.title || "");
    setNamePronunciation(user?.name_pronounciation);
    setPhone(user?.phone || "");
  }, [user]);

  const handleSave = async () => {
    const payload = {
      full_name: fullName,
      avatar_url: image,
      display_name: displayName,
      username: displayName,
      phone,
      email: user?.email,
      title,
      timezone,
      name_pronounciation: namePronunciation,
    };

    setButtonLoading(true);

    try {
      const res = await axios.patch(BASE_URL + "/profile", payload, {
        headers: {
          Authorization: `Bearer ${state?.token}`,
          "Content-Type": "multipart/form-data",
        },
      });

      if (res?.status === 200 || res?.status === 201) {
        dispatch({
          type: ACTIONS.PROFILE_CALLBACK,
          payload: !state?.profileCallback,
        });
        cogoToast.success(res?.data?.message);
        // UpdateAvatarInLocalStorage()
      }

      setTimeout(() => {
        setButtonLoading(false);
        onClose();
      }, 1000);
    } catch (err) {
      console.log(err);
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file?.type !== "image/jpeg" && file?.type !== "image/png") {
      return cogoToast.error("Image type is not supported");
    }

    if (file) {
      const url = URL.createObjectURL(file);
      setAvatar(url);

      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemovePhoto = async () => {
    try {
      await axios.delete(BASE_URL + "/profile/image", {
        headers: {
          Authorization: `Bearer ${state?.token}`,
          "Content-Type": "application/json",
        },
      });

      dispatch({
        type: ACTIONS.PROFILE_CALLBACK,
        payload: !state?.profileCallback,
      });

      setAvatar(images?.user);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.log(error);
      setAvatar(images?.user);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] p-0 overflow-hidden max-h-[90vh]">
        <DialogHeader className={`p-6 pb-0`}>
          <DialogTitle className="text-[#1D2939] text-lg lg:text-xl font-black">
            Edit your profile
          </DialogTitle>
        </DialogHeader>

        <div
          ref={contentRef}
          className={`max-h-[calc(90vh-180px)] border-t border-[#E6EAEF] space-y-5 pb-6 overflow-y-auto scrollbar-hide`}
        >
          <div className="flex flex-col-reverse md:flex-row">
            <div className="flex-1 py-3 px-6 space-y-5">
              {/* Full Name */}
              <div className="space-y-2">
                <label
                  htmlFor="fullName"
                  className="text-sm text-[#101828] font-bold"
                >
                  Display Name
                </label>
                <input
                  id="fullName"
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"
                />
              </div>

              {/* Display Name */}
              <div className="space-y-2">
                <label
                  htmlFor="displayName"
                  className="text-sm text-[#101828] font-bold"
                >
                  Username
                </label>
                <input
                  id="displayName"
                  type="text"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  className="w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"
                />
                <p className="text-xs text-[#667085]">
                  This could be your first name, or a nickname - however
                  you&apos;d like people to refer to you.
                </p>
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="displayName"
                  className="text-sm text-[#101828] font-bold"
                >
                  Phone Number
                </label>
                <input
                  id="displayName"
                  type="text"
                  value={phone}
                  placeholder="+44 20 7946 0958"
                  onChange={(e) => setPhone(e.target.value)}
                  className="w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"
                />
              </div>

              {/* Title/Role */}
              <div className="space-y-2">
                <label
                  htmlFor="title"
                  className="text-sm text-[#101828] font-bold"
                >
                  Title
                </label>
                <input
                  id="title"
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"
                />
              </div>
            </div>

            {/* Right side - Profile Picture */}
            <div className="w-[230px] pl-6 pr-3 py-3 space-y-6">
              <div className="space-y-2">
                <h3 className="text-sm text-[#101828] font-bold">
                  Profile Photo
                </h3>
                <div className="flex flex-col items-start gap-4">
                  <div className="relative w-[192px] h-[192px] border rounded-[9px]">
                    <Image
                      src={avatar}
                      alt={fullName}
                      width={192}
                      height={192}
                      className="rounded-[9px] object-cover w-full h-full"
                    />
                  </div>
                  <div className="space-y-1 w-full">
                    <Button
                      variant="outline"
                      onClick={triggerFileInput}
                      className="text-[13px] h-8 text-[#344054] border-[#E6EAEF] gap-2 w-full flex items-center justify-center"
                    >
                      <Upload size={14} />
                      <span>Upload photo</span>
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleFileUpload}
                    />
                    <Button
                      onClick={handleRemovePhoto}
                      className="text-[13px] h-8 text-[#6868F7] gap-2 py-0 w-full"
                    >
                      Remove photo
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Name Recording */}
          {/* <div className="space-y-2 px-6">
                        <label
                            htmlFor="nameRecording"
                            className="text-sm text-[#101828] font-bold"
                        >
                            Name Recording
                        </label>
                        <Button
                            variant="outline"
                            className="text-sm h-8 text-[#344054] border-[#E6EAEF] gap-1 w-fit flex"
                        >
                            <Mic size={14} />
                            <span>Record Audio Clip</span>
                        </Button>
                    </div> */}

          {/* Name Pronunciation */}
          <div className="space-y-2 px-6">
            <label
              htmlFor="namePronunciation"
              className="text-sm text-[#101828] font-bold"
            >
              Name Pronunciation
            </label>
            <input
              id="namePronunciation"
              type="text"
              value={namePronunciation}
              onChange={(e) => setNamePronunciation(e.target.value)}
              placeholder="Zoe (pronounced 'zo-ee')"
              className="w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"
            />
            <p className="text-xs text-[#667085]">
              This could be a phonetic pronunciation, or an example of something
              your name sounds like.
            </p>
          </div>

          {/* Timezone */}
          <div className="space-y-2 px-6">
            <label
              htmlFor="timezone"
              className="text-sm text-[#101828] font-bold"
            >
              Timezone
            </label>
            <select
              id="timezone"
              value={timezone}
              onChange={(e) => setTimezone(e.target.value)}
              className="w-full form-select px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"
            >
              {timezones.map((tz, index) => (
                <option key={index} value={tz}>
                  {tz}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex justify-end gap-3 px-6 py-5 border-t border-[#E6EAEF] sticky bottom-0 bg-white">
          <Button
            variant="outline"
            onClick={onClose}
            className="text-sm text-[#344054] h-9 border-[#E6EAEF]"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="d-flex items-center gap-2 bg-[#6868F7] text-white h-9 text-sm hover:bg-[#5151d3]"
            disabled={buttonLoading}
          >
            Save Changes {buttonLoading && <Loading />}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditProfileDialog;
