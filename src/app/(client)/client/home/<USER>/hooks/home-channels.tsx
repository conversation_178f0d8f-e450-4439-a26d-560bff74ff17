import { useContext, useEffect } from "react";
import { ACTIONS } from "~/store/Actions";
import { DataContext } from "~/store/GlobalState";
import { GetRequest } from "~/utils/new-request";

const UseHomeChannel = () => {
  const { state, dispatch } = useContext(DataContext);
  const orgId = localStorage.getItem("orgId") || "";

  // fetch all user channels
  useEffect(() => {
    if (orgId && state?.token) {
      const fetchChannels = async () => {
        // get all channels in organisation
        const res = await GetRequest(`/organisations/${orgId}/user-channels`);

        if (res?.status === 200 || res?.status === 201) {
          dispatch({ type: ACTIONS.CHANNELS, payload: res?.data?.data });
        }
      };

      const fetchAllChannels = async () => {
        const res = await GetRequest(
          `/organisations/${orgId}/channels?limit=100`
        );

        if (res?.status === 200 || res?.status === 201) {
          dispatch({ type: ACTIONS.ALL_CHANNELS, payload: res?.data?.data });
        }
      };

      const fetchArchivedChannels = async () => {
        const archivedChannelsRes = await GetRequest(
          `/organisations/${orgId}/channels/archived`
        );

        if (
          archivedChannelsRes?.status === 200 ||
          archivedChannelsRes?.status === 201
        ) {
          dispatch({
            type: ACTIONS.ARCHIVED_CHANNELS,
            payload: archivedChannelsRes?.data?.data,
          });
        }
      };

      // get other channels
      // const otherChannelsRes = await GetRequest(
      //   `/organisations/${orgId}/user-not-channels`
      // );

      // if (
      //   otherChannelsRes?.status === 200 ||
      //   otherChannelsRes?.status === 201
      // ) {
      //   dispatch({
      //     type: ACTIONS.OTHER_CHANNELS,
      //     payload: otherChannelsRes?.data?.data,
      //   });
      // }

      dispatch({
        type: ACTIONS.CHANNEL_LOADING,
        payload: false,
      });

      fetchChannels();
      fetchAllChannels();
      fetchArchivedChannels();
    }
  }, [orgId, state?.channelCallback, dispatch]);

  useEffect(() => {
    if (orgId) {
      const fetchChannels = async () => {
        const res = await GetRequest(`/organisations/${orgId}/dms`);

        if (res?.status === 200 || res?.status === 201) {
          dispatch({ type: ACTIONS.DMS, payload: res?.data?.data });
        }
      };
      fetchChannels();
    }
  }, [orgId, dispatch, state?.groupCallback, state?.dmCount]);

  useEffect(() => {
    if (orgId) {
      const fetchBots = async () => {
        const res = await GetRequest(`/organisations/${orgId}/fetch-bots`);

        if (res?.status === 200 || res?.status === 201) {
          dispatch({ type: ACTIONS.AGENT_DM, payload: res?.data?.data });
        }
      };
      fetchBots();
    }
  }, [orgId, dispatch, state?.agentCallback]);

  // get workflows
  useEffect(() => {
    if (orgId) {
      const fetchWorkflows = async () => {
        const res = await GetRequest(`/organisations/${orgId}/workflows`);

        if (res?.status === 200 || res?.status === 201) {
          dispatch({ type: ACTIONS.WORKFLOWS, payload: res?.data?.data });
        }
      };
      fetchWorkflows();
    }
  }, [orgId, dispatch, state?.workflowCallback]);

  return <></>;
};

export default UseHomeChannel;
