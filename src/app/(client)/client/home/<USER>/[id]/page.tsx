"use client";

import { useEffect, useState } from "react";
import {
  Search,
  Star,
  MessageCircleIcon,
  CheckCircle2,
  Copy,
} from "lucide-react";
import Image from "next/image";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import images from "~/assets/images";
import { GetRequest } from "~/utils/new-request";
import Loading from "~/components/ui/loading";
import PageHeader from "../../../agents/components/page-header";
import { Button } from "~/components/ui/button";
import Skills from "../../../_components/colleagues/skills-tab";
import MessageInput from "../../../_components/colleagues/message-input";

export default function ColleageProfile() {
  const [activeTab, setActiveTab] = useState("Task List");
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  const [loading, setLoading] = useState(true);
  const searchParams = useSearchParams();
  const json = searchParams.get("json_url") || "";
  const [agent, setAgent] = useState<any>(null);
  const [copied, setCopied] = useState(false);

  // get single workflow
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";

    if (id) {
      const getWorkflows = async () => {
        const res = await GetRequest(`/organisations/${orgId}/workflows/${id}`);
        setAgent(res.data.data);
        setLoading(false);
      };
      getWorkflows();
    }
  }, [json, id]);

  const handleCopy = () => {
    navigator.clipboard.writeText(agent?.descriptions?.app_url);
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  const TABS = ["Task List", "Skills", "Prompts", "Data"];

  //

  return (
    <>
      <PageHeader
        title="AI Colleague Profile"
        buttonIcon={
          <Search
            size={20}
            className="text-gray-600 cursor-pointer hover:text-gray-800"
          />
        }
      />

      {loading ? (
        <div className="flex items-center justify-center mt-20">
          <Loading width="40" height="40" color="blue" />
        </div>
      ) : (
        <div className="bg-white p-4 md:p-6 mx-auto">
          {/* Header */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
            <div className="flex flex-col sm:flex-row items-center gap-4 w-full md:w-auto">
              <div className="w-[100px] h-[100px] min-w-[100px] border rounded-xl shadow-sm">
                <Image
                  src={agent?.descriptions?.app_logo || images.bot}
                  alt={agent?.descriptions?.app_name}
                  width={100}
                  height={100}
                  className="rounded-xl bg-green-100"
                />
              </div>

              <div className="w-full">
                <h2 className="text-lg font-semibold flex items-center gap-2 flex-wrap">
                  {agent?.name || "Juno - Report Assistant"}
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  {agent?.description ||
                    "Monitor the #sales Telegram group and email me when 'discount' is mentioned."}
                </p>
                <div className="flex gap-3 items-center mt-3">
                  <Button
                    variant={"outline"}
                    className="h-fit p-[7px] border-[#E6EAEF] hover:bg-gray-100"
                  >
                    <Star size={20} strokeWidth={1.5} color="#667085" />
                  </Button>

                  <Button
                    onClick={() => router.push(`/client/home/<USER>/${id}`)}
                    variant={"outline"}
                    className="h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1 hover:bg-gray-100"
                  >
                    <MessageCircleIcon size={18} /> Go to chats
                  </Button>

                  <div className="flex flex-wrap items-center gap-2 bg-indigo-50 px-2 py-2 rounded-md">
                    <a
                      href={agent?.provider?.url}
                      className="text-sm text-indigo-600 font-medium break-all"
                    >
                      {agent?.provider?.url ||
                        "https://telex.im/agents/juno-report-assistant"}
                    </a>
                    <button onClick={handleCopy} className="text-indigo-500">
                      {copied ? (
                        <CheckCircle2 className="w-4 h-4" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <hr className="my-5" />

          <MessageInput />

          {/* Tabs */}
          <div className="mt-8 border-b border-gray-200 overflow-x-auto">
            <div className="flex gap-4 min-w-max">
              {TABS.map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`px-3 py-2 text-sm font-medium whitespace-nowrap ${
                    activeTab === tab
                      ? "text-indigo-600 border-b-2 border-indigo-600"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === "Task List" && (
            <div className="mt-6">
              <h2 className="font-semibold text-sm text-gray-800 mb-4">
                Tasks Lists
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-5 items-start">
                {agent?.agents_details?.map((agent: any, index: number) => {
                  return (
                    <div
                      key={index}
                      className={`transition-all duration-300 border rounded-xl bg-white shadow-sm hover:border-[#D0D0FD] cursor-pointer`}
                    >
                      {/* Header */}
                      <div
                        // onClick={() => handleRoute(agent)}
                        className="flex items-center justify-between p-3 border-b"
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className={`relative h-9 w-9 rounded-lg flex items-center justify-center border border-[#E6EAEF]`}
                          >
                            <Image
                              src={agent?.app_logo || images.blueBot}
                              alt="Bot"
                              width={20}
                              height={20}
                              className="h-8 w-8"
                            />
                            <div className="absolute -bottom-[3px] -right-[3px] w-[10px] h-[10px] bg-[#6DC347] rounded-full border border-white" />
                          </div>
                          <div>
                            <h2 className="text-sm font-semibold text-[#344054]">
                              {agent.name}
                            </h2>
                          </div>
                        </div>
                        <div className="text-xs text-[#667085] border border-[#D0D5DD] rounded-full px-2 py-[2px]">
                          {index + 1}
                        </div>
                      </div>

                      {/* <div>
                          <p className="mt-2 text-sm text-[#475467] px-3 ">
                            {agent.app_description}
                          </p>

                          <div
                            className={`transition-[max-height] duration-300 ease-in-out overflow-hidden mt-3 ${isActive ? "max-h-[500px]" : "max-h-0"
                              }`}
                          >
                            <div className="mt-4 py-3 border-t text-xs text-[#667085] text-center">
                              Used by 30+ companies in simplifying their workflow
                            </div>
                          </div>
                        </div> */}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === "Skills" && <Skills />}
        </div>
      )}
    </>
  );
}
