"use client";

import { Search, EllipsisVertical, PlusIcon } from "lucide-react";
import PageHeader from "../../agents/components/page-header";
import { useContext, useEffect, useState, useCallback, useRef } from "react";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { GetRequest } from "~/utils/new-request";
import { Button } from "~/components/ui/button";
import { useRouter } from "next/navigation";
import WorkflowsMarketPlace from "../../_components/workflows/workflow-marketplace";
import InActiveWorkflows from "../../_components/workflows/inactive-workflow";
import ActiveWorkflows from "../../_components/workflows/active-workflow";

const BrowseAgents = () => {
  const { state, dispatch } = useContext(DataContext);
  const [activeTopTab, setActiveTopTab] = useState(state?.topLabel);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const getIntegrations = useCallback(async () => {
    const active = state?.workflows?.filter(
      (item: any) => item?.is_active === true
    );
    const inactive = state?.workflows?.filter(
      (item: any) => item?.is_active === false
    );

    dispatch({ type: ACTIONS.ACTIVE_WORKFLOWS, payload: active });
    dispatch({ type: ACTIONS.INACTIVE_WORKFLOWS, payload: inactive });
  }, []);

  useEffect(() => {
    getIntegrations();
  }, [getIntegrations]);

  // Fetch general Workflows
  useEffect(() => {
    const getIntegrations = async () => {
      const res = await GetRequest("/agents");
      if (res?.status === 200 || res?.status == 201) {
        dispatch({
          type: ACTIONS.MARKETPLACE_AGENTS,
          payload: res?.data?.data,
        });
      }
    };
    getIntegrations();
  }, [dispatch]);

  const handleRoute = (label: string) => {
    dispatch({ type: ACTIONS.TOP_LABEL, payload: label });
    setActiveTopTab(label);
  };

  // Handle popover click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node)
      ) {
        setIsPopoverOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="w-full">
      <PageHeader
        title="Browse Workflows"
        buttonIcon={
          <Search
            size={20}
            className="text-gray-600 cursor-pointer hover:text-gray-800"
          />
        }
        moreIcon={
          <div className="relative" ref={popoverRef}>
            <EllipsisVertical
              className="w-5 h-5 text-gray-600 cursor-pointer hover:text-gray-800"
              onClick={() => setIsPopoverOpen(!isPopoverOpen)}
            />
            {isPopoverOpen && (
              <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-20">
                <div className="py-1">
                  <Button
                    onClick={() => router.push("/client/workflows/new")}
                    className="flex items-center justify-start w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 border-none bg-transparent"
                  >
                    <PlusIcon className="w-5 h-5" color="#8686F9" />
                    <span className="ml-1 text-[13px] font-semibold text-blue-200">
                      Add new workflow
                    </span>
                  </Button>
                </div>
              </div>
            )}
          </div>
        }
      />

      <div className="py-3">
        {/* Top Tabs */}
        <div className={`flex items-center text-sm space-x-6 px-4 border-b`}>
          <span
            onClick={() => handleRoute("Active")}
            className={`cursor-pointer flex items-center pb-4 ${
              activeTopTab === "Active"
                ? "text-black font-semibold border-b-2 border-blue-300"
                : "text-gray-500"
            }`}
          >
            Active
            <span
              className={`ml-1 px-2 py-0.5 rounded-full text-xs ${
                activeTopTab === "Active"
                  ? "bg-purple-100 text-purple-600"
                  : "bg-gray-100"
              }`}
            >
              {state?.activeWorkflows?.length || 0}
            </span>
          </span>

          <span
            onClick={() => handleRoute("Inactive")}
            className={`cursor-pointer flex items-center pb-4 ${
              activeTopTab === "Inactive"
                ? "text-black font-semibold border-b-2 border-blue-300"
                : "text-gray-500"
            }`}
          >
            Inactive
            <span
              className={`ml-1 px-2 py-0.5 rounded-full text-xs ${
                activeTopTab === "Inactive"
                  ? "bg-purple-100 text-purple-600"
                  : "bg-gray-100"
              }`}
            >
              {state?.inactiveWorkflows?.length || 0}
            </span>
          </span>

          <span
            onClick={() => handleRoute("Workflow Marketplace")}
            className={`cursor-pointer flex items-center pb-4 ${
              activeTopTab === "Workflow Marketplace"
                ? "text-black font-semibold border-b-2 border-blue-300"
                : "text-gray-500"
            }`}
          >
            Workflow Marketplace
            <span
              className={`ml-1 px-2 py-0.5 rounded-full text-xs ${
                activeTopTab === "Workflow Marketplace"
                  ? "bg-purple-100 text-purple-600"
                  : "bg-gray-100"
              }`}
            >
              {state?.marketPlaceWorkflows?.length || 0}
            </span>
          </span>
        </div>

        <div>
          {activeTopTab === "Active" && <ActiveWorkflows />}
          {activeTopTab === "Inactive" && <InActiveWorkflows />}
          {activeTopTab === "Workflow Marketplace" && <WorkflowsMarketPlace />}
        </div>
      </div>
    </div>
  );
};

export default BrowseAgents;
