"use client";
import { useState, useMemo, useEffect } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  closestCenter,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import { AgentsList } from "../../_components/workflows/agent-list";
import { WorkflowCanvas } from "../../_components/workflows/workflow-canva";
import { AgentCard } from "../../_components/workflows/agent-card";
import { WorkflowAgent } from "~/types/workflow";
import NewWorkflowHeader from "../../_components/workflow-nav/new";
import { GetRequest } from "~/utils/new-request";

export default function Workflow() {
  const [workflowAgents, setWorkflowAgents] = useState<WorkflowAgent[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isOver, setIsOver] = useState(false);
  const [mockAgents, setMockAgents] = useState<any>([]);

  // Fetch Integrations
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";
    const getIntegrations = async () => {
      const res = await GetRequest(`/organisations/${orgId}/agents`);
      if (res?.status === 200 || res?.status == 201) {
        setMockAgents(res.data.data);
      }
    };
    getIntegrations();
  }, []);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const activeAgent = useMemo(() => {
    if (!activeId) return null;
    return mockAgents.find((agent: any) => agent.id === activeId);
  }, [activeId]);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;
    setIsOver(over?.id === "workflow-canvas");
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);
    setIsOver(false);

    if (!over) return;

    const isOverWorkflowCanvas =
      over.id === "workflow-canvas" ||
      workflowAgents.some((wa) => wa.id === over.id);

    if (isOverWorkflowCanvas && !active.id.toString().startsWith("workflow-")) {
      const agentId = active.id as string;

      // Find the selected agent from mockAgents
      const selectedAgent = mockAgents.find(
        (agent: any) => agent.id === agentId
      );

      const newWorkflowAgent: WorkflowAgent = {
        id: `workflow-${crypto.randomUUID()}`,
        agentId,
        position: workflowAgents.length,
        app_name: selectedAgent?.app_name || "",
        app_description: selectedAgent?.app_description || "",
      };
      setWorkflowAgents((prev) => [...prev, newWorkflowAgent]);
      return;
    }

    // Reordering within workflow canvas
    if (active.id !== over.id && active.id.toString().startsWith("workflow-")) {
      setWorkflowAgents((prev) => {
        const oldIndex = prev.findIndex((item) => item.id === active.id);
        const newIndex = prev.findIndex((item) => item.id === over.id);

        if (oldIndex === -1 || newIndex === -1) return prev;

        const reordered = arrayMove(prev, oldIndex, newIndex);
        return reordered.map((item, index) => ({
          ...item,
          position: index,
        }));
      });
    }
  };

  const handleRemoveAgent = (workflowAgentId: string) => {
    setWorkflowAgents((prev) =>
      prev
        .filter((wa) => wa.id !== workflowAgentId)
        .map((item, index) => ({ ...item, position: index }))
    );
  };

  //

  return (
    <div className="flex flex-col h-[calc(100vh-70px)] relative w-full overflow-hidden">
      <NewWorkflowHeader agents={workflowAgents} />
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="bg-primary-50">
          <div className="container mx-auto p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-[calc(100vh-200px)]">
              {/* Left Panel - Agents List */}
              <div className="bg-card border border-border rounded-lg shadow-sm overflow-hidden">
                <AgentsList agents={mockAgents} draggedAgent={activeId} />
              </div>

              {/* Right Panel - Workflow Canvas */}
              <div className="bg-card border border-border rounded-lg shadow-sm overflow-hidden">
                <WorkflowCanvas
                  workflowAgents={workflowAgents}
                  agents={mockAgents}
                  onRemoveAgent={handleRemoveAgent}
                  isOver={isOver}
                />
              </div>
            </div>
          </div>
        </div>

        <DragOverlay>
          {activeAgent ? (
            <div className="rotate-3 scale-105">
              <AgentCard agent={activeAgent} isDragging />
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
