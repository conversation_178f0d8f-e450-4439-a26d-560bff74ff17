"use client";
import React, { useState } from "react";
import SettingsLabel from "../../components/settings-label";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "~/components/ui/tabs";
import { Button } from "~/components/ui/button";
import InviteModal from "../../../_components/user-management/invite-modal";
import MembersTable from "../../../_components/user-management/members-table";
import InviteTable from "../../../_components/user-management/invite-table";
import { DataContext } from "~/store/GlobalState";
import { useContext } from "react";
import { ACTIONS } from "~/store/Actions";

const Page = () => {
  const { state, dispatch } = useContext(DataContext);
  // eslint-disable-next-line
  const [membersCount, setMembersCount] = useState<number>(0);
  // eslint-disable-next-line
  const [invitesCount, setInvitesCount] = useState<number>(0);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  const { orgMembers, orgInvites } = state;

  const members = orgMembers?.filter((item: any) => item.role !== "bot");

  // Handle optimistic invite updates
  const handleInviteSuccess = (newInvites: any[]) => {
    const currentInvites = orgInvites || [];
    const updatedInvites = [...newInvites, ...currentInvites];
    dispatch({ type: ACTIONS.ORG_INVITES, payload: updatedInvites });
  };

  return (
    <div>
      <SettingsLabel />
      <InviteModal
        isOpen={isInviteModalOpen}
        onClose={() => setIsInviteModalOpen(false)}
        onInviteSuccess={handleInviteSuccess}
      />
      <div className="p-4 lg:px-8">
        <div className="mb-6 flex justify-between items-start">
          <div>
            <h1 className="text-base font-semibold">Your Team</h1>
            <p className="text-sm text-[#344054]">
              Manage all members of your team.
            </p>
          </div>
          <Button
            className="px-4 py-2 bg-[#7141F8] text-white rounded-md hover:bg-[#7141F8]/80 transition-colors"
            onClick={() => setIsInviteModalOpen(true)}
          >
            Invite People
          </Button>
        </div>

        <Tabs defaultValue="members" className="w-full mt-6">
          <div className="border-b border-gray-200">
            <TabsList className="flex w-fit bg-white rounded-none h-auto p-0 space-x-10">
              <TabsTrigger
                value="members"
                className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-2"
              >
                Members
                <div className="flex items-center justify-center rounded-full bg-[#F2F4F7] text-[#5757CD] tracking-[-0.5%] font-semibold text-xs px-2 py-1 data-[state=active]:block data-[state=inactive]:hidden">
                  {members ? members.length : 0}
                </div>
              </TabsTrigger>

              <TabsTrigger
                value="invites"
                className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-2"
                data-testid="invites-tab"
              >
                Invites
                <div className="flex items-center justify-center rounded-full bg-[#F2F4F7] text-[#5757CD] tracking-[-0.5%] font-semibold text-xs px-2 py-1 data-[state=active]:block data-[state=inactive]:hidden">
                  {orgInvites ? orgInvites.length : 0}
                </div>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="members" className="mt-6">
            <MembersTable membersData={members} />
          </TabsContent>

          <TabsContent value="invites" className="mt-6 ">
            <InviteTable invitesData={orgInvites} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Page;
