{"name": "telex_fe", "version": "0.14.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0 && echo -e '\\033[32mLinting was successful!\\033[0m'", "lint:fix": "eslint src --ext ts,tsx --fix && echo -e '\\033[32mLinting was successful!\\033[0m'", "format": "prettier --write 'src/**/*.{ts,tsx,scss,html}'", "email": "email dev --dir src/emails", "cypress": "cypress open"}, "dependencies": {"@dicebear/bottts": "^9.2.3", "@dicebear/collection": "^9.2.3", "@dicebear/core": "^9.2.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@mdx-js/mdx": "^3.0.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.2.0", "@react-email/components": "0.0.22", "@react-oauth/google": "^0.12.2", "@tanstack/react-table": "^8.19.3", "@tiptap/core": "^2.11.5", "@tiptap/extension-bold": "^2.11.9", "@tiptap/extension-bullet-list": "^2.6.4", "@tiptap/extension-code": "^2.6.2", "@tiptap/extension-code-block": "^2.6.2", "@tiptap/extension-focus": "^2.6.6", "@tiptap/extension-hard-break": "^2.6.4", "@tiptap/extension-image": "^2.6.6", "@tiptap/extension-italic": "^2.11.9", "@tiptap/extension-link": "^2.6.2", "@tiptap/extension-list-item": "^2.6.2", "@tiptap/extension-mention": "^2.6.2", "@tiptap/extension-ordered-list": "^2.6.2", "@tiptap/extension-paragraph": "^2.6.4", "@tiptap/extension-placeholder": "^2.6.2", "@tiptap/extension-strike": "^2.11.9", "@tiptap/react": "^2.6.2", "@tiptap/starter-kit": "^2.6.2", "aos": "^2.3.4", "axios": "^1.7.3", "centrifuge": "^5.2.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cogo-toast": "^4.2.3", "country-state-city": "^3.2.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.1.7", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.11.1", "emojione": "^4.5.0", "framer-motion": "^11.3.25", "input-otp": "^1.2.4", "lodash.debounce": "^4.0.8", "lucide-react": "^0.417.0", "moment": "^2.30.1", "next": "14.2.5", "nuqs": "^1.17.8", "open-graph-scraper": "^6.9.0", "prismjs": "^1.30.0", "react": "^18", "react-avatar-group": "^1.0.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-email": "2.1.6", "react-google-login": "^5.2.2", "react-infinite-scroll-component": "^6.1.0", "react-loader-spinner": "^6.1.6", "react-loading-skeleton": "^3.5.0", "react-markdown": "^9.1.0", "react-parallax-tilt": "^1.7.295", "react-pdf": "^9.2.1", "react-phone-input-2": "^2.15.1", "react-select": "^5.8.3", "react-slick": "^0.30.2", "react-syntax-highlighter": "^15.5.0", "react-toastify": "^10.0.5", "read-time-estimate": "^0.0.3", "recharts": "^2.12.7", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark": "^15.0.1", "remark-emoji": "^5.0.1", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "remark-math": "^6.0.0", "remark-toc": "^9.0.0", "sass": "^1.77.8", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "swiper": "^11.1.14", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "timeago.js": "^4.0.2", "use-debounce": "^10.0.3", "uuidv7": "^1.0.1"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^10.3.5", "@tailwindcss/typography": "^0.5.14", "@types/aos": "^3.0.7", "@types/emojione": "^2.2.9", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "@types/react-syntax-highlighter": "^15.5.13", "@types/read-time-estimate": "^0.0.2", "autoprefixer": "^10.4.19", "cypress": "^13.13.2", "eslint": "^8", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "lint-staged": "^15.2.7", "postcss": "^8.4.40", "prettier": "^3.3.3", "semantic-release": "^24.2.3", "tailwindcss": "^3.4.7", "typescript": "^4.9.5"}, "lint-staged": {"src/**/*.{ts,tsx}": ["pnpm run lint", "pnpm run format"]}, "packageManager": "pnpm@9.7.1+sha512.faf344af2d6ca65c4c5c8c2224ea77a81a5e8859cbc4e06b1511ddce2f0151512431dd19e6aff31f2c6a8f5f2aced9bd2273e1fed7dd4de1868984059d2c4247"}